import { extract<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/utils';
import { createStep, createWorkflow } from '@mastra/core/workflows';
import { CoreMessage, TextPart } from 'ai';
import { z } from 'zod';
import {
  SCOUTING_CONFIG,
  videoScouter,
} from '@/services/scouting/videoScouter';
import { CreatorFilterOutputSchema } from '@/agents/creatorFilterAgent';

// Timing utilities
const formatDuration = (ms: number): string => {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}m`;
};

const createStepSeparator = (
  stepName: string,
  stepId: string,
  action: 'START' | 'END',
  duration?: number,
): string => {
  const separator = '='.repeat(80);
  const timestamp = new Date().toISOString();
  const durationText = duration ? ` (${formatDuration(duration)})` : '';
  return `\n${separator}\n🚀 STEP ${action}: ${stepName.toUpperCase()} [${stepId}]${durationText}\n⏰ ${timestamp}\n${separator}`;
};

// Global workflow timing
let workflowStartTime: number;

// Core workflow input parameters
const workflowInputSchema = z.object({
  targetCreatorDescription: z.string(),
  useIntelligentChallengeSelection: z.boolean().optional().default(false),
  desiredCreatorCount: z.number().optional().default(50),
  filterMode: z.enum(['STRICT', 'LOOSE']).optional().default('STRICT'),
});

// Common pass-through fields that flow through all steps
const sharedParameterSchema = z.object({
  targetCreatorDescription: z.string(),
  useIntelligentChallengeSelection: z.boolean(),
  desiredCreatorCount: z.number(),
  filterMode: z.enum(['STRICT', 'LOOSE']),
  scoutGuidance: z.string().optional(),
});

// Hashtag analysis results
const hashtagResultsSchema = z.object({
  core: z.array(z.string()),
  adjacent: z.array(z.string()),
  reason: z.string().optional(),
});

// Challenge selection results
const challengeResultsSchema = z.object({
  challengeIds: z.array(z.string()),
  keywords: z.array(z.string()),
});

// Creator scouting statistics
const scoutingStatsSchema = z.object({
  challengeVideosCollected: z.number(),
  uniqueCreatorsFound: z.number(),
  creatorsProcessed: z.number(),
  successfulCreators: z.number(),
  totalCreatorVideos: z.number(),
  totalVideosProcessed: z.number(),
});

// Creator and video data
const creatorDataSchema = z.object({
  creators: z.array(z.any()), // Enhanced creator data with videoCount
  videos: z.array(z.any()), // Creator videos (optimized)
  stats: scoutingStatsSchema,
});

/**
 * Extract essential video data for filtering (reduce payload size)
 * @param video Full video object
 * @returns Minimal video data for context
 */
function extractVideoEssentials(video: any) {
  return {
    video_id: video.video_id,
    description: video.description, // Contains title and hashtags
    view_count: video.view_count,
    like_count: video.like_count,
    comment_count: video.comment_count,
    share_count: video.share_count,
  };
}

// Schema for the hashtag search results
const searchHashtagSchema = z.object({
  core: z.array(z.string()),
  adjacent: z.array(z.string()),
  reason: z.string().optional(),
});

// Enhanced workflow output schema with additional statistics
const enhancedWorkflowOutputSchema = z.object({
  desiredCreators: z.number(),
  scoutedCreators: z.number(),
  results: z.array(
    z.object({
      url: z.string().url(),
      reason: z.string(),
      match_score: z.number().optional(),
      tier: z.enum(['PERFECT', 'EXCELLENT', 'GOOD', 'ACCEPTABLE']).optional(),
    }),
  ),
  filterSummary: z
    .object({
      mode: z.enum(['STRICT', 'LOOSE']),
      total_analyzed: z.number(),
      total_qualified: z.number(),
      avg_match_score: z.number(),
      tier_breakdown: z.object({
        PERFECT: z.number(),
        EXCELLENT: z.number(),
        GOOD: z.number(),
        ACCEPTABLE: z.number(),
      }),
    })
    .optional(),
});

export const creatorScoutWorkflow = createWorkflow({
  id: 'creator-scout',
  inputSchema: workflowInputSchema,
  outputSchema: enhancedWorkflowOutputSchema,
});

// Step 1: Analyze the user's requirements and generate hashtags
const analyzeRequirementStep = createStep({
  id: 'analyzeRequirement',
  inputSchema: workflowInputSchema,
  resumeSchema: z.object({
    userInputMessage: z.string().optional(),
    messages: z.array(z.any()),
  }),
  outputSchema: hashtagResultsSchema.merge(sharedParameterSchema),
  execute: async ({ inputData, resumeData, suspend, mastra }) => {
    // Initialize workflow timing on first step
    if (!workflowStartTime) {
      workflowStartTime = Date.now();
    }

    const stepStartTime = Date.now();
    console.log(
      createStepSeparator(
        'Analyze Requirements',
        'analyzeRequirement',
        'START',
      ),
    );
    console.log('📋 Analyzing user requirements and generating hashtags...');
    console.log('🎯 Target:', inputData.targetCreatorDescription);
    console.log('🔧 Filter Mode:', inputData.filterMode || 'STRICT');
    console.log('📊 Desired Count:', inputData.desiredCreatorCount || 50);
    // console.log('inputData', inputData);
    // console.log('resumeData', resumeData);

    const scoutAgent = mastra?.getAgent('creatorHashtagScout');
    if (!scoutAgent) {
      throw new Error('Campaign analyzer agent not found');
    }

    // Initialize messages array
    let messages: CoreMessage[] = [];

    // Check if this is a resume (either resumeData exists OR inputData has been contaminated with resume data)
    const isResume = resumeData?.messages || (inputData as any).messages;

    if (isResume) {
      // If we have cached messages, use them (this is a resume)
      console.log('Found cached messages, resuming conversation');

      // Get messages from resumeData first, fallback to inputData if framework merged them
      const cachedMessages =
        resumeData?.messages || (inputData as any).messages;
      messages = [...cachedMessages] as CoreMessage[]; // Create a copy to avoid mutation

      // Get user input message from resumeData first, fallback to inputData
      const userInputMessage =
        resumeData?.userInputMessage || (inputData as any).userInputMessage;

      // Add the user's response from the resumed workflow
      if (userInputMessage) {
        console.log('user responded:', userInputMessage);
        const userResponse: CoreMessage = {
          role: 'user',
          content: userInputMessage,
        };
        messages.push(userResponse);
      }
    } else {
      // If no cached messages, start a new conversation
      console.log('No cached messages found, starting new conversation');
      const description = inputData.targetCreatorDescription;
      const userDescription: CoreMessage = {
        role: 'user',
        content: description,
      };
      messages.push(userDescription);
    }

    console.log('About to call agent with messages:', messages.length);
    // Generate a response from the agent
    const resp = await scoutAgent.generate(messages);
    // console.log('Agent response received');
    const assistantMessage = resp.response.messages[0];
    const content = (assistantMessage.content as Array<TextPart>)[0];

    let parsedResult;
    // Check if the response is in the expected format
    try {
      // Extract the first JSON object from the response
      parsedResult = extractFirstJson(content.text);
      console.log('assistant response:', content.text);
      // console.log('parsedResult', parsedResult);
    } catch (e) {
      // JSON parsing failed, continue to suspend
    }

    const parseResult = searchHashtagSchema.safeParse(parsedResult);
    if (parseResult.success) {
      const stepDuration = Date.now() - stepStartTime;
      console.log('✅ Successfully generated hashtags:', parseResult.data);
      console.log(
        createStepSeparator(
          'Analyze Requirements',
          'analyzeRequirement',
          'END',
          stepDuration,
        ),
      );

      // console.log('parseResult.data', parseResult.data);
      return {
        ...parseResult.data,
        targetCreatorDescription: inputData.targetCreatorDescription,
        useIntelligentChallengeSelection:
          inputData.useIntelligentChallengeSelection ?? false,
        desiredCreatorCount: inputData.desiredCreatorCount ?? 50,
        filterMode: inputData.filterMode ?? 'LOOSE',
        scoutGuidance: content.text, // Use full response as guidance
      };
    }

    // If not in expected format, add assistant message to conversation and suspend
    const updatedMessages = [...messages, assistantMessage];
    // console.log('appended messages', messages);
    // console.log('updatedMessages', updatedMessages);

    // console.log('before suspend, we are here');

    // Suspend and wait for user input
    // When resumed, the step will restart with the updated messages in resumeData
    await suspend({
      messages: updatedMessages,
      message: content,
    });

    // console.log('after suspend, we are here, returning empty hashtags');

    // This code should not execute in normal operation since suspend should restart the step
    // But we need to return something to satisfy TypeScript
    return {
      core: [],
      adjacent: [],
      targetCreatorDescription: inputData.targetCreatorDescription,
      useIntelligentChallengeSelection:
        inputData.useIntelligentChallengeSelection ?? false,
      desiredCreatorCount: inputData.desiredCreatorCount ?? 50,
      filterMode: inputData.filterMode ?? 'LOOSE',
      scoutGuidance: 'No guidance available',
    };
  },
});

// Step 2: Select challenges from keywords
const selectChallengesStep = createStep({
  id: 'select-challenges',
  inputSchema: hashtagResultsSchema.merge(sharedParameterSchema),
  outputSchema: challengeResultsSchema.merge(sharedParameterSchema),
  execute: async ({ inputData, mastra }) => {
    const stepStartTime = Date.now();
    console.log(
      createStepSeparator('Select Challenges', 'select-challenges', 'START'),
    );

    const {
      core,
      adjacent,
      targetCreatorDescription,
      useIntelligentChallengeSelection,
      desiredCreatorCount,
      filterMode,
      scoutGuidance,
    } = inputData;

    const allKeywords = [...core, ...adjacent];
    console.log('🔍 Selecting challenges from keywords:', allKeywords);
    console.log(
      '🧠 Using intelligent selection:',
      useIntelligentChallengeSelection,
    );
    console.log('🎯 Target creator count:', desiredCreatorCount);

    const challengePickerAgent = mastra?.getAgent('challengePickerAgent');
    if (!challengePickerAgent) {
      throw new Error('Challenge picker agent not found');
    }

    const challengeIds = await videoScouter.selectChallengesFromKeywords(
      allKeywords,
      {
        useIntelligentSelection: useIntelligentChallengeSelection,
        targetCreatorDescription,
        scoutGuidance,
        agent: challengePickerAgent,
      },
    );

    const stepDuration = Date.now() - stepStartTime;
    console.log(
      `✅ Selected ${challengeIds.length} challenges for creator scouting`,
    );
    console.log(
      createStepSeparator(
        'Select Challenges',
        'select-challenges',
        'END',
        stepDuration,
      ),
    );

    return {
      challengeIds,
      keywords: allKeywords,
      targetCreatorDescription,
      useIntelligentChallengeSelection,
      desiredCreatorCount,
      filterMode,
      scoutGuidance,
    };
  },
});

// Step 3: Scout creators from challenges
const scoutCreatorsStep = createStep({
  id: 'scout-creators',
  inputSchema: challengeResultsSchema.merge(sharedParameterSchema),
  outputSchema: creatorDataSchema.merge(
    sharedParameterSchema.pick({
      targetCreatorDescription: true,
      filterMode: true,
      scoutGuidance: true,
      desiredCreatorCount: true,
    }),
  ),
  execute: async ({ inputData }) => {
    const stepStartTime = Date.now();
    console.log(
      createStepSeparator('Scout Creators', 'scout-creators', 'START'),
    );

    const {
      challengeIds,
      desiredCreatorCount,
      targetCreatorDescription,
      filterMode,
      scoutGuidance,
    } = inputData;

    console.log(`🕵️ Scouting creators from ${challengeIds.length} challenges`);
    console.log(`🎯 Target creator count: ${desiredCreatorCount}`);
    console.log(`🔧 Filter mode: ${filterMode}`);

    if (challengeIds.length === 0) {
      const stepDuration = Date.now() - stepStartTime;
      console.warn('⚠️  No challenges selected, returning empty results');
      console.log(
        createStepSeparator(
          'Scout Creators',
          'scout-creators',
          'END',
          stepDuration,
        ),
      );
      return {
        creators: [],
        videos: [],
        stats: {
          challengeVideosCollected: 0,
          uniqueCreatorsFound: 0,
          creatorsProcessed: 0,
          successfulCreators: 0,
          totalCreatorVideos: 0,
          totalVideosProcessed: 0,
        },
        targetCreatorDescription,
        filterMode,
        scoutGuidance,
        desiredCreatorCount,
      };
    }

    // Scout creators from each challenge and combine results
    const allCreators: any[] = [];
    const allVideos: any[] = [];
    let totalStats = {
      challengeVideosCollected: 0,
      uniqueCreatorsFound: 0,
      creatorsProcessed: 0,
      successfulCreators: 0,
      totalCreatorVideos: 0,
      totalVideosProcessed: 0,
    };

    // Calculate creators per challenge to reach desired total
    // const creatorsPerChallenge = Math.ceil(
    //   desiredCreatorCount / challengeIds.length,
    // );
    const creatorsPerChallenge = desiredCreatorCount;

    for (const [idx, challengeId] of challengeIds) {
      try {
        console.log(`Scouting challenge: ${challengeId}`);
        const result = await videoScouter.scoutChallengeCreators(
          challengeId,
          creatorsPerChallenge,
        );

        allCreators.push(...result.creators);
        allVideos.push(...result.videos);

        // Accumulate stats
        totalStats.challengeVideosCollected +=
          result.stats.challengeVideosCollected;
        totalStats.uniqueCreatorsFound += result.stats.uniqueCreatorsFound;
        totalStats.creatorsProcessed += result.stats.creatorsProcessed;
        totalStats.successfulCreators += result.stats.successfulCreators;
        totalStats.totalCreatorVideos += result.stats.totalCreatorVideos;
        totalStats.totalVideosProcessed += result.stats.totalVideosProcessed;

        console.log(
          `Challenge ${challengeId} yielded ${result.creators.length} creators [${idx + 1}/${challengeIds.length}]`,
        );
      } catch (error) {
        console.error(`Error scouting challenge ${challengeId}:`, error);
        // Continue with next challenge
      }
    }

    // Deduplicate creators by unique_id
    const creatorMap = new Map();
    const uniqueCreators = [];

    for (const creator of allCreators) {
      if (!creatorMap.has(creator.unique_id)) {
        creatorMap.set(creator.unique_id, creator);
        uniqueCreators.push(creator);
      }
    }

    const stepDuration = Date.now() - stepStartTime;
    console.log(
      `✅ Final results: ${uniqueCreators.length} unique creators, ${allVideos.length} videos`,
    );
    console.log(
      createStepSeparator(
        'Scout Creators',
        'scout-creators',
        'END',
        stepDuration,
      ),
    );

    return {
      creators: uniqueCreators,
      videos: allVideos,
      stats: totalStats,
      targetCreatorDescription,
      filterMode,
      scoutGuidance,
      desiredCreatorCount,
    };
  },
});

// Step 4: Filter creators using the creator filter agent
const filterCreatorsStep = createStep({
  id: 'filter-creators',
  inputSchema: creatorDataSchema.merge(
    sharedParameterSchema.pick({
      targetCreatorDescription: true,
      filterMode: true,
      scoutGuidance: true,
      desiredCreatorCount: true,
    }),
  ),
  outputSchema: enhancedWorkflowOutputSchema,
  execute: async ({ inputData, mastra }) => {
    const stepStartTime = Date.now();
    console.log(
      createStepSeparator('Filter Creators', 'filter-creators', 'START'),
    );

    const {
      creators,
      videos,
      targetCreatorDescription,
      desiredCreatorCount,
      filterMode,
      scoutGuidance,
    } = inputData;
    console.log(`🔍 Filtering ${creators.length} creators in batches`);
    console.log(
      `📹 Using ${videos.length} creator videos for enhanced filtering`,
    );
    console.log(`🔧 Filter mode: ${filterMode}`);
    console.log('🎯 Original requirements:', targetCreatorDescription);
    console.log(`📊 Target Creator Count: ${desiredCreatorCount}`);

    const creatorFilterAgent = mastra?.getAgent('creatorFilterAgent');
    if (!creatorFilterAgent) {
      throw new Error('Creator filter agent not found');
    }

    const batchSize = SCOUTING_CONFIG.CREATOR_BATCH_SIZE;
    const allQualifiedCreators: Array<{
      unique_id: string;
      collect_reason: string;
      match_score?: number;
      tier?: 'PERFECT' | 'EXCELLENT' | 'GOOD' | 'ACCEPTABLE';
    }> = [];

    // Track overall filtering statistics
    let totalAnalyzed = 0;
    let totalQualified = 0;
    let totalMatchScore = 0;
    const tierCounts = { PERFECT: 0, EXCELLENT: 0, GOOD: 0, ACCEPTABLE: 0 };

    // Create a map of creator videos for enhanced filtering (using essential data only)
    const creatorVideosMap = new Map<string, any[]>();
    for (const video of videos) {
      const creatorId = video.author.unique_id;
      if (!creatorVideosMap.has(creatorId)) {
        creatorVideosMap.set(creatorId, []);
      }
      // Store only essential video data to reduce payload size
      creatorVideosMap.get(creatorId)!.push(extractVideoEssentials(video));
    }

    // Process creators in batches with intelligent stopping logic
    for (let i = 0; i < creators.length; i += batchSize) {
      const batch = creators.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      const remainingCreators = creators.length - i - batchSize;

      console.log(
        `Processing batch ${batchNumber} with ${batch.length} creators (${remainingCreators > 0 ? remainingCreators + ' remaining' : 'final batch'})`,
      );

      try {
        // Enhance creator data with their videos for better filtering
        const enhancedBatch = batch.map((creator) => {
          const creatorVideos = creatorVideosMap.get(creator.unique_id) || [];
          return {
            ...creator,
            recentVideos: creatorVideos.slice(
              0,
              SCOUTING_CONFIG.MAX_VIDEOS_FOR_CONTEXT,
            ), // Include recent videos for context
            totalVideosAnalyzed: creatorVideos.length,
          };
        });

        // Construct enhanced prompt with filter mode, requirements, and scout guidance
        let prompt = `Filter Mode: ${filterMode}\n\nCreator Requirements: ${targetCreatorDescription}`;

        if (scoutGuidance) {
          prompt += `\n\nScout Guidance: ${scoutGuidance}`;
        }

        prompt += `\n\nNote: Each creator now includes their recent video content for better assessment of their style and audience fit.`;
        prompt += `\n\nCreator Data: ${JSON.stringify(enhancedBatch, null, 2)}`;

        const userMessage: CoreMessage = {
          role: 'user',
          content: prompt,
        };

        try {
          const resp = await creatorFilterAgent.generate([userMessage], {
            output: CreatorFilterOutputSchema,
          });

          const object = resp.object;

          // Update statistics
          totalAnalyzed += object.summary.total_analyzed;
          totalQualified += object.summary.total_qualified;
          totalMatchScore +=
            object.summary.avg_match_score * object.summary.total_qualified;

          // Update tier counts
          tierCounts.PERFECT += object.summary.tier_breakdown.PERFECT;
          tierCounts.EXCELLENT += object.summary.tier_breakdown.EXCELLENT;
          tierCounts.GOOD += object.summary.tier_breakdown.GOOD;
          tierCounts.ACCEPTABLE += object.summary.tier_breakdown.ACCEPTABLE;

          // Add qualified creators with enhanced data
          const enhancedCreators = object.qualified_kols.map((creator) => ({
            unique_id: creator.unique_id,
            collect_reason: creator.collect_reason,
            match_score: creator.match_score,
            tier: creator.tier,
          }));

          allQualifiedCreators.push(...enhancedCreators);

          console.log(
            `Batch ${batchNumber} yielded ${object.qualified_kols.length} qualified creators (Mode: ${object.mode}, Avg Score: ${object.summary.avg_match_score.toFixed(2)})`,
          );

          // Intelligent stopping logic
          const currentPerfectCount = tierCounts.PERFECT;
          const currentTotalQualified = allQualifiedCreators.length;

          console.log(
            `Current status: ${currentTotalQualified} total qualified, ${currentPerfectCount} PERFECT tier (target: ${desiredCreatorCount})`,
          );

          // Early termination condition 1: We have enough PERFECT tier creators
          if (currentPerfectCount >= desiredCreatorCount) {
            console.log(
              `✅ Found enough PERFECT tier creators (${currentPerfectCount} >= ${desiredCreatorCount}). Stopping early.`,
            );
            break;
          }

          // Early termination condition 2: We have enough total creators and no more batches to process
          if (
            currentTotalQualified >= desiredCreatorCount &&
            remainingCreators <= 0
          ) {
            console.log(
              `✅ Found enough total qualified creators (${currentTotalQualified} >= ${desiredCreatorCount}) and no more creators to process.`,
            );
            break;
          }

          // Continue processing condition: We have enough total but want more PERFECT tier
          if (
            currentTotalQualified >= desiredCreatorCount &&
            remainingCreators > 0
          ) {
            console.log(
              `🔄 Have enough total creators (${currentTotalQualified}) but only ${currentPerfectCount} PERFECT tier. Continuing to find more PERFECT matches...`,
            );
          }
        } catch (error) {
          console.error(`Error processing batch ${batchNumber}:`, error);
        }
      } catch (error) {
        console.error(`Error processing batch ${batchNumber}:`, error);
        // Continue with next batch
      }
    }

    console.log(
      `Total qualified creators found: ${allQualifiedCreators.length}`,
    );

    // Calculate overall average match score
    const avgMatchScore =
      totalQualified > 0 ? totalMatchScore / totalQualified : 0;

    // Sort creators by match_score in descending order (highest scores first)
    const sortedCreators = allQualifiedCreators.sort((a, b) => {
      // Sort by match_score descending, then by tier priority (PERFECT > EXCELLENT > GOOD > ACCEPTABLE)
      const tierPriority = { PERFECT: 4, EXCELLENT: 3, GOOD: 2, ACCEPTABLE: 1 };
      const scoreA = a.match_score || 0;
      const scoreB = b.match_score || 0;
      const tierA = tierPriority[a.tier || 'ACCEPTABLE'];
      const tierB = tierPriority[b.tier || 'ACCEPTABLE'];

      // Primary sort: match_score descending
      if (scoreA !== scoreB) {
        return scoreB - scoreA;
      }
      // Secondary sort: tier priority descending
      return tierB - tierA;
    });

    // Convert to the expected output format with enhanced data
    const results = sortedCreators.map((creator) => ({
      url: `https://www.tiktok.com/@${creator.unique_id}`,
      reason: creator.collect_reason,
      match_score: creator.match_score,
      tier: creator.tier,
    }));

    // Enhanced logging for final results
    const perfectCount = tierCounts.PERFECT;
    const totalFound = allQualifiedCreators.length;

    console.log(`\n📊 FINAL FILTERING RESULTS:`);
    console.log(`Target: ${desiredCreatorCount} creators`);
    console.log(`Found: ${totalFound} total qualified creators`);
    console.log(`PERFECT tier: ${perfectCount} creators`);
    console.log(
      `Results exceed target: ${totalFound > desiredCreatorCount ? 'Yes' : 'No'}`,
    );
    console.log(
      `Enough PERFECT tier: ${perfectCount >= desiredCreatorCount ? 'Yes' : 'No'}`,
    );

    console.log(
      'Enhanced results with scores and tiers (sorted by match_score):',
      results.slice(0, 5),
    ); // Show top 5
    console.log(
      `Filter Summary - Mode: ${filterMode}, Avg Score: ${avgMatchScore.toFixed(2)}, Tier Distribution:`,
      tierCounts,
    );

    // Calculate step and total workflow duration
    const stepDuration = Date.now() - stepStartTime;
    const totalWorkflowDuration = Date.now() - workflowStartTime;

    console.log(
      createStepSeparator(
        'Filter Creators',
        'filter-creators',
        'END',
        stepDuration,
      ),
    );

    // Final workflow summary
    const workflowSeparator = '🎉'.repeat(80);
    console.log(`\n${workflowSeparator}`);
    console.log(`🏁 WORKFLOW COMPLETED SUCCESSFULLY!`);
    console.log(`⏱️  Total Duration: ${formatDuration(totalWorkflowDuration)}`);
    console.log(`📊 Final Results: ${totalFound} qualified creators found`);
    console.log(`🏆 PERFECT Tier: ${perfectCount} creators`);
    console.log(
      `🎯 Target Met: ${totalFound >= desiredCreatorCount ? '✅ YES' : '❌ NO'}`,
    );
    console.log(`${workflowSeparator}\n`);

    const resultCreators = {
      desiredCreators: creators.length,
      scoutedCreators: allQualifiedCreators.length,
      results,
      filterSummary: {
        mode: filterMode,
        total_analyzed: totalAnalyzed,
        total_qualified: totalQualified,
        avg_match_score: avgMatchScore,
        tier_breakdown: tierCounts,
      },
    };

    console.log(resultCreators);

    return resultCreators;
  },
});

// Define the workflow steps and their relationships
creatorScoutWorkflow
  .then(analyzeRequirementStep)
  .then(selectChallengesStep)
  .then(scoutCreatorsStep)
  .then(filterCreatorsStep)
  .commit();
